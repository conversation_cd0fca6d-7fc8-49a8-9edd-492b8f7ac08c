/**
 * 认证服务
 */
import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { User } from '../users/entities/user.entity';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  /**
   * 验证用户
   */
  async validateUser(usernameOrEmail: string, password: string): Promise<User> {
    return this.usersService.validateUser(usernameOrEmail, password);
  }

  /**
   * 登录
   */
  async login(user: User) {
    const payload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
    };

    return {
      access_token: this.jwtService.sign(payload),
      user,
    };
  }

  /**
   * 注册
   */
  async register(username: string, email: string, password: string, displayName?: string) {
    // 输入验证
    if (!username || !email || !password) {
      throw new Error('用户名、邮箱和密码不能为空');
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('邮箱格式不正确');
    }

    // 用户名长度验证
    if (username.length < 3 || username.length > 50) {
      throw new Error('用户名长度必须在3-50个字符之间');
    }

    // 密码长度验证
    if (password.length < 6) {
      throw new Error('密码长度不能少于6个字符');
    }

    try {
      const user = await this.usersService.create({
        username,
        email,
        password,
        displayName,
      });

      return this.login(user);
    } catch (error) {
      // 重新抛出错误，保持原有的错误信息
      throw error;
    }
  }

  /**
   * 验证JWT令牌
   */
  async validateJwt(payload: any): Promise<any> {
    const user = await this.usersService.findById(payload.sub);
    if (!user) {
      throw new Error('用户不存在');
    }
    return user;
  }


}
