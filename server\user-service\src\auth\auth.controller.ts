/**
 * 认证控制器
 */
import { Controller, Post, Body, UseGuards, Get, Request, ConflictException, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { MessagePattern, RpcException } from '@nestjs/microservices';
import { AuthService } from './auth.service';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';

@ApiTags('认证')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @UseGuards(LocalAuthGuard)
  @Post('login')
  @ApiOperation({ summary: '用户登录' })
  @ApiResponse({ status: 200, description: '登录成功' })
  async login(@Request() req, @Body() _loginDto: LoginDto) {
    return this.authService.login(req.user);
  }

  @Post('register')
  @ApiOperation({ summary: '用户注册' })
  @ApiResponse({ status: 201, description: '注册成功' })
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(
      registerDto.username,
      registerDto.email,
      registerDto.password,
      registerDto.displayName,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiResponse({ status: 200, description: '返回当前用户信息' })
  getProfile(@Request() req) {
    return req.user;
  }

  // 微服务消息处理
  @MessagePattern({ cmd: 'validateUser' })
  async validateUser(data: { usernameOrEmail: string; password: string }) {
    return this.authService.validateUser(data.usernameOrEmail, data.password);
  }

  @MessagePattern({ cmd: 'validateJwt' })
  async validateJwt(payload: any) {
    return this.authService.validateJwt(payload);
  }

  @MessagePattern({ cmd: 'register' })
  async registerUser(data: { username: string; email: string; password: string; displayName?: string }) {
    try {
      return await this.authService.register(data.username, data.email, data.password, data.displayName);
    } catch (error) {
      console.error('Auth Controller 注册错误:', {
        error: error.message,
        errorName: error.name,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });

      // 处理ConflictException（用户名或邮箱已存在）
      if (error instanceof ConflictException || error.message?.includes('已存在')) {
        throw new RpcException({
          statusCode: 409,
          message: error.message,
          error: 'Conflict'
        });
      }

      // 处理BadRequestException（输入验证错误）
      if (error instanceof BadRequestException ||
          error.message?.includes('不能为空') ||
          error.message?.includes('格式不正确') ||
          error.message?.includes('长度')) {
        throw new RpcException({
          statusCode: 400,
          message: error.message,
          error: 'Bad Request'
        });
      }

      // 其他错误
      throw new RpcException({
        statusCode: 500,
        message: error.message || '内部服务器错误',
        error: 'Internal Server Error'
      });
    }
  }


}
