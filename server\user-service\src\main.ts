/**
 * 用户服务入口文件
 */
import { NestFactory } from '@nestjs/core';
import { Transport } from '@nestjs/microservices';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as compression from 'compression';
import helmet from 'helmet';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';

async function bootstrap() {
  // 创建Nest应用实例
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 配置微服务
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('USER_SERVICE_HOST', 'localhost'),
      port: configService.get<number>('USER_SERVICE_PORT', 3001),
    },
  });

  // 配置HTTP服务
  // 全局前缀
  app.setGlobalPrefix('api');

  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // 注释掉全局异常过滤器，因为它会干扰微服务的RpcException传递
  // app.useGlobalFilters(new GlobalExceptionFilter());

  // 启用CORS
  app.enableCors();

  // 启用压缩
  app.use(compression());

  // 启用安全头
  app.use(helmet());

  // Swagger文档
  const config = new DocumentBuilder()
    .setTitle('用户服务API')
    .setDescription('DL（Digital Learning）引擎用户服务API文档')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // 启动微服务
  await app.startAllMicroservices();

  // 启动HTTP服务
  const httpPort = configService.get<number>('USER_SERVICE_HTTP_PORT', 4001);
  await app.listen(httpPort);
  console.log(
    `用户服务已启动，微服务端口: ${configService.get<number>('USER_SERVICE_PORT', 3001)}, HTTP端口: ${httpPort}`,
  );
}

bootstrap();
