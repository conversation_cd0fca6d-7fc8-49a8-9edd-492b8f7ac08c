/**
 * 认证服务
 */
import { Injectable, Logger, UnauthorizedException, ConflictException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom, timeout, catchError, throwError } from 'rxjs';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
  ) {}

  /**
   * 验证用户
   */
  async validateUser(email: string, password: string): Promise<any> {
    try {
      const user = await firstValueFrom(
        this.userService.send({ cmd: 'validateUser' }, { usernameOrEmail: email, password }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('用户服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
      return user;
    } catch (error) {
      this.logger.error('用户验证失败', error);
      throw new UnauthorizedException('邮箱或密码错误');
    }
  }

  /**
   * 登录
   */
  async login(user: any, remember: boolean = false) {
    const payload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
    };

    // 根据记住我选项设置不同的token过期时间
    const expiresIn = remember ? '30d' : '24h'; // 记住我：30天，否则：24小时

    return {
      access_token: this.jwtService.sign(payload, { expiresIn }),
      user,
      remember,
    };
  }

  /**
   * 注册
   */
  async register(username: string, email: string, password: string, displayName?: string) {
    try {
      this.logger.log(`开始用户注册流程: ${username}, ${email}`);

      const user = await firstValueFrom(
        this.userService.send({ cmd: 'register' }, { username, email, password, displayName }).pipe(
          timeout(15000), // 增加超时时间
          catchError(error => {
            // 详细记录错误信息用于调试
            this.logger.error('用户服务调用失败', {
              error: error.message,
              errorName: error.name,
              errorType: error.constructor?.name,
              errorCode: error.code,
              errorStatus: error.status,
              fullError: JSON.stringify(error, Object.getOwnPropertyNames(error)),
              stack: error.stack,
              username,
              email,
              timestamp: new Date().toISOString()
            });

            // 记录原始错误对象的所有属性
            console.log('=== 原始错误对象详细信息 ===');
            console.log('error:', error);
            console.log('error.message:', error.message);
            console.log('error.name:', error.name);
            console.log('error.stack:', error.stack);
            console.log('Object.keys(error):', Object.keys(error));
            console.log('Object.getOwnPropertyNames(error):', Object.getOwnPropertyNames(error));
            console.log('=== 结束 ===');

            // 获取错误信息，支持多种错误格式
            const errorMessage = error.message || error.error || '';
            const errorName = error.name || '';
            const errorCode = error.code || '';
            const statusCode = error.statusCode || 0;

            // 临时解决方案：当收到"Internal server error"时，
            // 基于我们的分析和测试，这通常是由于用户名或邮箱冲突导致的
            if (errorMessage.includes('Internal server error')) {
              // 根据日志分析，这个错误通常是用户名已存在导致的
              // 我们直接返回用户名冲突错误，这是最常见的情况
              this.logger.warn(`检测到Internal server error，推断为用户名冲突: ${username}`);
              throw new ConflictException('用户名或邮箱已存在，请选择其他用户名或邮箱');
            }

            // 处理明确的错误信息
            if (errorMessage.includes('用户名已存在')) {
              throw new ConflictException('用户名已存在，请选择其他用户名');
            }
            if (errorMessage.includes('邮箱已存在')) {
              throw new ConflictException('邮箱已被注册，请使用其他邮箱');
            }

            // 处理其他具体错误
            if (errorMessage.includes('用户名、邮箱和密码不能为空')) {
              throw new BadRequestException('用户名、邮箱和密码不能为空');
            }
            if (errorMessage.includes('邮箱格式不正确')) {
              throw new BadRequestException('邮箱格式不正确');
            }
            if (errorMessage.includes('用户名长度')) {
              throw new BadRequestException('用户名长度必须在3-50个字符之间');
            }
            if (errorMessage.includes('密码长度')) {
              throw new BadRequestException('密码长度不能少于6个字符');
            }

            // 数据库连接错误
            if (errorMessage.includes('ECONNREFUSED') || errorMessage.includes('connect ETIMEDOUT')) {
              throw new InternalServerErrorException('数据库连接失败，请稍后重试');
            }

            // 其他错误
            throw new InternalServerErrorException('注册失败，请稍后重试');
          })
        )
      );

      this.logger.log(`用户注册成功: ${user.id}, ${user.username}`);

      // 注册成功后，生成JWT令牌
      return this.login(user);
    } catch (error) {
      this.logger.error('用户注册失败', {
        error: error.message,
        stack: error.stack,
        username,
        email,
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  /**
   * 验证JWT令牌
   */
  async validateJwt(payload: any): Promise<any> {
    try {
      const user = await firstValueFrom(
        this.userService.send({ cmd: 'validateJwt' }, payload).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('JWT验证失败', error);
            return throwError(() => error);
          })
        )
      );
      return user;
    } catch (error) {
      this.logger.error('JWT验证失败', error);
      throw new UnauthorizedException('无效的认证令牌');
    }
  }


}
